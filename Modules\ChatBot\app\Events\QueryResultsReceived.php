<?php

namespace Modules\ChatBot\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\ChatBot\Models\Query;

class QueryResultsReceived implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Query $query;
    public array $broadcastData;

    /**
     * Create a new event instance.
     */
    public function __construct(Query $query, array $broadcastData)
    {
        $this->query = $query;
        $this->broadcastData = $broadcastData;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        $channels = [];

        logger()->info('Broadcasting query results------------>', $this->query->toArray());

        // Broadcast to conversation channel if available
        if ($this->query->conversation_id && $this->query->conversation) {
            $conversationUuid = $this->query->conversation->uuid;
            $channels[] = new PrivateChannel("conversation.{$conversationUuid}");
        }

        // Also broadcast to user's personal channel
        $channels[] = new PrivateChannel("user.{$this->query->owner_id}");

        return $channels;
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return $this->broadcastData;
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'QueryResultsReceived';
    }

    /**
     * Determine if this event should broadcast.
     */
    public function shouldBroadcast(): bool
    {
        return $this->query->status === 'completed' && !empty($this->query->results);
    }
}
