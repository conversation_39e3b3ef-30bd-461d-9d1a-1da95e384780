<?php

namespace Modules\ChatBot\Jobs;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Modules\ChatBot\Models\Query;
use Modules\ChatBot\Models\Message;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Events\QueryResultsReceived;
use Modules\ChatBot\Facades\AIFacade;
use Modules\ChatBot\Enums\MessageStatus;
use Exception;

class LLMProcessingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected Query $query;
    protected array $ragResults;

    public int $tries = 3;
    public int $timeout = 300;

    public function __construct(Query $query, array $ragResults = [])
    {
        $this->query = $query;

        // Ensure RAG results are properly encoded for serialization
        $this->ragResults = $this->sanitizeRagResults($ragResults);

        $this->onQueue(config('chatbot.llm.queue_name', 'llm-processing'));
    }

    /**
     * Sanitize RAG results to ensure proper UTF-8 encoding
     */
    private function sanitizeRagResults(array $ragResults): array
    {
        $sanitized = [];
        foreach ($ragResults as $result) {
            if (is_array($result)) {
                $sanitizedResult = [];
                foreach ($result as $key => $value) {
                    if (is_string($value)) {
                        $sanitizedResult[$key] = mb_convert_encoding($value, 'UTF-8', 'UTF-8');
                    } else {
                        $sanitizedResult[$key] = $value;
                    }
                }
                $sanitized[] = $sanitizedResult;
            } else {
                $sanitized[] = $result;
            }
        }
        return $sanitized;
    }

    /**
     * @throws Exception
     */
    public function handle(): void
    {
        try {
            $conversation = Conversation::find($this->query->conversation_id);
            $bot = $conversation->bot;
            if (!$conversation || !$bot) {
                throw new Exception('Conversation or bot not found');
            }

            $userMessage = Message::find($this->query->message_id);
            if (!$userMessage) {
                throw new Exception('User message not found');
            }

            $assistantMessageId = $this->query->metadata['assistant_message_id'] ?? null;
            if (!$assistantMessageId) {
                throw new Exception('Assistant message ID not found in query metadata');
            }

            $assistantMessage = Message::find($assistantMessageId);
            if (!$assistantMessage) {
                throw new Exception("Assistant message not found: {$assistantMessageId}");
            }

            $messages = $this->getConversationContext($conversation);

            $ragContext = $this->buildContextFromRAG();

            $enhancedUserMessage = $this->enhanceUserMessageWithRAG($userMessage, $ragContext);

            // Validate enhanced message before proceeding
            if (empty($enhancedUserMessage)) {
                throw new Exception('Enhanced user message is empty');
            }

            $this->updateAssistantMessageWithRAGContext($assistantMessage, $ragContext);

            $success = AIFacade::getAssistantPrompts($bot, $userMessage, $assistantMessage, $messages, $enhancedUserMessage);

            if (!$success) {
                throw new Exception('AI service failed to generate response');
            }

            $this->query->markAsCompleted([
                'rag_results' => $this->ragResults,
                'assistant_message_id' => $assistantMessage->id,
                'context_used' => $ragContext,
                'processing_time' => now()->diffInSeconds($this->query->processing_started_at),
            ], [
                'llm_processing_completed_at' => now()->toISOString(),
                'total_processing_time' => now()->diffInSeconds($this->query->processing_started_at),
                'workflow_type' => 'rag_llm_enhanced',
                'assistant_message_id' => $assistantMessage->id,
            ]);

            $this->broadcastFinalResults();

        } catch (Exception $e) {
            Log::error('LLMProcessingJob failed--------->:::', [
                'error' => $e->getMessage(),
            ]);
            $this->query->markAsFailed($e->getMessage(), [
                'llm_job_failed_at' => now()->toISOString(),
                'attempt' => $this->attempts(),
                'rag_results_available' => !empty($this->ragResults),
            ]);
            throw $e;
        }
    }

    private function updateAssistantMessageWithRAGContext(Message $assistantMessage, string $ragContext): void
    {
        $assistantMessage->update([
            'metadata' => array_merge($assistantMessage->metadata ?? [], [
                'query_id' => $this->query->uuid,
                'rag_enhanced' => true,
                'rag_sources_count' => count($this->ragResults),
                'rag_context' => $ragContext,
                'llm_processing_started_at' => now()->toISOString(),
            ])
        ]);
    }

    private function getConversationContext(Conversation $conversation): Collection
    {
        return Message::where('conversation_id', $conversation->id)
            ->whereIn('role', ['user', 'assistant'])
            ->whereIn('status', [MessageStatus::COMPLETED, MessageStatus::PENDING])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get()
            ->reverse();
    }

    private function buildContextFromRAG(): string
    {
        if (empty($this->ragResults)) return '';

        $contextParts = [];
        foreach ($this->ragResults as $result) {
            if (!empty($result['snippet'])) {
                $score = isset($result['score']) ? number_format($result['score'] * 100, 1) : 'N/A';
                // Ensure proper UTF-8 encoding for snippet content
                $snippet = mb_convert_encoding($result['snippet'], 'UTF-8', 'UTF-8');
                $contextParts[] = "Relevance: {$score}% - {$snippet}";
            }
        }

        $context = implode("\n\n", $contextParts);

        // Ensure final context is properly encoded
        return mb_convert_encoding($context, 'UTF-8', 'UTF-8');
    }

    /**
     * @throws Exception
     */
    private function enhanceUserMessageWithRAG(Message $userMessage, string $ragContext): string
    {
        // Validate user message content
        if (empty($userMessage->content) || is_null($userMessage->content)) {
            throw new Exception('User message content is null or empty');
        }

        // Ensure proper UTF-8 encoding for Vietnamese content
        $userContent = mb_convert_encoding($userMessage->content, 'UTF-8', 'UTF-8');
        $ragContext = mb_convert_encoding($ragContext, 'UTF-8', 'UTF-8');

        $enhancedContent = '';

        if (empty($ragContext)) {
            Log::info('RAG context is empty. Creating a strict fallback prompt.');

            $enhancedContent = "Bối cảnh: Không có thông tin nào trong tài liệu chuyên ngành được tìm thấy để trả lời câu hỏi của người dùng.\n\n";
            $enhancedContent .= "=== CÂU HỎI CỦA NGƯỜI DÙNG ===\n";
            $enhancedContent .= $userMessage->content . "\n\n";

            $enhancedContent .= "=== HƯỚNG DẪN XỬ LÝ NHIỆM VỤ ===\n";
            $enhancedContent .= "Bạn phải thực hiện theo các bước sau:\n";
            $enhancedContent .= "1. **Phân tích câu hỏi:** Đầu tiên, hãy tự xác định xem `CÂU HỎI CỦA NGƯỜI DÙNG` thuộc loại nào:\n";
            $enhancedContent .= "   - **Loại A (Chuyên môn):** Các câu hỏi về luật, nghị định, thủ tục, quy định mà đáng lẽ phải có trong tài liệu chuyên ngành.\n";
            $enhancedContent .= "   - **Loại B (Xả giao/Khác):** Các câu hỏi chào hỏi, giao tiếp thông thường, hoặc các câu hỏi kiến thức chung không liên quan.\n\n";

            $enhancedContent .= "2. **Hành động dựa trên phân loại:**\n";
            $enhancedContent .= "   - **Nếu câu hỏi thuộc Loại A:** BẠN BẮT BUỘC phải trả lời rằng bạn không tìm thấy thông tin trong tài liệu. Hãy trả lời một cách lịch sự, tuân thủ vai trò trợ lý hành chính công của bạn.\n";
            $enhancedContent .= "   - **Nếu câu hỏi thuộc Loại B:** Hãy trả lời câu hỏi đó một cách tự nhiên, thân thiện, dựa trên kiến thức chung và vai trò của bạn. Đừng đề cập đến việc tìm kiếm tài liệu.\n\n";

            $enhancedContent .= "3. **QUAN TRỌNG:** Đừng giải thích quá trình phân loại của bạn. Chỉ đưa ra câu trả lời cuối cùng.";

        } else {
            Log::info('RAG context found. Creating a new direct instruction prompt.');

            $enhancedContent = "=== NGỮ CẢNH ĐƯỢC CUNG CẤP ===\n";
            $enhancedContent .= "Dưới đây là toàn bộ thông tin bạn bắt buộc phải sử dụng:\n\n";
            $enhancedContent .= $ragContext . "\n\n";

            $enhancedContent .= "=== CÂU HỎI ===\n";
            $enhancedContent .= $userContent . "\n\n";

            // Yêu cầu được làm lại để trực tiếp và giảm sự phức tạp
            $enhancedContent .= "=== HƯỚNG DẪN BẮT BUỘC ===\n";
            $enhancedContent .= "1. Đọc kỹ `CÂU HỎI` và `NGỮ CẢNH ĐƯỢC CUNG CẤP`.\n";
            $enhancedContent .= "2. Nhiệm vụ của bạn là trả lời `CÂU HỎI` **chỉ và chỉ dựa vào** thông tin có trong `NGỮ CẢNH ĐƯỢC CUNG CẤP`.\n";
            $enhancedContent .= "3. Cấm tuyệt đối sử dụng kiến thức bên ngoài hoặc suy diễn thông tin không có trong ngữ cảnh.\n";
            $enhancedContent .= "4. Nếu `NGỮ CẢNH ĐƯỢC CUNG CẤP` chứa thông tin liên quan, hãy tổng hợp và diễn giải lại thành một câu trả lời hoàn chỉnh, mượt mà.\n";
            $enhancedContent .= "5. Nếu `NGỮ CẢNH ĐƯỢC CUNG CẤP` **không chứa** bất kỳ thông tin nào để trả lời câu hỏi, hãy trả về CHÍNH XÁC chuỗi sau và không thêm gì khác: `Tôi không tìm thấy thông tin phù hợp trong tài liệu được cung cấp.`";
        }

        // Ensure final content is properly encoded
        $finalContent = mb_convert_encoding($enhancedContent, 'UTF-8', 'UTF-8');

        // Validate final content is not empty
        if (empty($finalContent)) {
            throw new Exception('Enhanced content is empty after processing');
        }

        return $finalContent;
    }

    private function broadcastFinalResults(): void
    {
        event(new QueryResultsReceived($this->query, [
            'query_id' => $this->query->uuid,
            'question' => $this->query->question,
            'results' => $this->query->getFormattedResults(),
            'timestamp' => now()->toISOString(),
            'bot_id' => $this->query->bot_id,
            'conversation_id' => $this->query->conversation_id,
            'message_id' => $this->query->message_id,
            'status' => $this->query->status,
            'metadata' => $this->query->metadata,
            'llm_enhanced' => true,
        ]));
    }

    public function failed(Exception $exception): void
    {
        Log::error('LLMProcessingJob permanently failed--------->:::', [
            'error' => $exception->getMessage()
        ]);

        $this->query->markAsFailed($exception->getMessage(), [
            'llm_permanently_failed_at' => now()->toISOString(),
            'total_attempts' => $this->attempts(),
            'rag_results_available' => !empty($this->ragResults),
        ]);
    }

    public function tags(): array
    {
        return [
            'llm',
            'query:' . $this->query->uuid,
            'owner:' . $this->query->owner_id,
            'bot:' . ($this->query->bot_id ?? 'none'),
        ];
    }

    public function backoff(): array
    {
        return [30, 60, 120];
    }
}
