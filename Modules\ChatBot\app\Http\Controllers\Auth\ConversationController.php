<?php

namespace Modules\ChatBot\Http\Controllers\Auth;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Modules\ChatBot\Facades\AIFacade;
use Modules\ChatBot\Http\Requests\ConversationRequest;
use Modules\ChatBot\Facades\ConversationFacade;
use Modules\ChatBot\Models\Conversation;
use Modules\Core\Traits\ResponseTrait;

class ConversationController extends Controller
{
    use ResponseTrait;


    /**
     * Display a listing of conversations.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $conversations = ConversationFacade::getUserConversations($request->all());
            return $this->successResponse($conversations, 'Conversations retrieved successfully.');
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    public function show(string $id)
    {
        $conversation = ConversationFacade::getByUuuid($id);
        if (!$conversation) {
            return $this->errorResponse('Conversation not found', 404);
        }
        return $this->successResponse($conversation, 'Conversation successfully!');
    }

    public function generalTitleConversation($id): JsonResponse
    {
        try {
            $content = AIFacade::generalTitleConversation($id);
            return $this->successResponse($content);
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e);
        }
    }

    /**
     * Create a new conversation.
     */
    public function createOrUpdateConversation(ConversationRequest $request): JsonResponse
    {
        try {
            $conversation = ConversationFacade::createOrUpdateConversation($request->validated());
            return $this->successResponse(
                $conversation,
                'Conversation created successfully.',
                201
            );
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    /**
     * Create a new conversation.
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $conversation = ConversationFacade::updateConversation($id, $request->all());
            return $this->successResponse(
                $conversation,
                __('Conversation was updated successfully'),
            );
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    public function delete(string $uuid)
    {
        $conversation = ConversationFacade::deleteByUuid($uuid);
        if (!$conversation) {
            return $this->errorResponse('Conversation not found', 404);
        }
        return $this->successResponse($uuid, __('Conversation deleted successfully!'));
    }

}
